from django.contrib import admin
from .models import Customer, Analysis, Dataset


@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    list_display = ['user', 'company_name', 'phone_number', 'created_at']
    list_filter = ['created_at']
    search_fields = ['user__username', 'user__email', 'company_name']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(Analysis)
class AnalysisAdmin(admin.ModelAdmin):
    list_display = ['name', 'user', 'is_active', 'created_at', 'last_edited_at']
    list_filter = ['is_active', 'created_at', 'last_edited_at']
    search_fields = ['name', 'user__username', 'description']
    readonly_fields = ['created_at', 'last_edited_at']
    list_editable = ['is_active']


@admin.register(Dataset)
class DatasetAdmin(admin.ModelAdmin):
    list_display = ['name', 'analysis', 'user', 'status', 'file_size', 'created_at']
    list_filter = ['status', 'created_at']
    search_fields = ['name', 'analysis__name', 'user__username']
    readonly_fields = ['created_at', 'updated_at']
    list_editable = ['status']
