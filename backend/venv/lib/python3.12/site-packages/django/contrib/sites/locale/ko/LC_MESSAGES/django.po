# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON>, <PERSON> <<EMAIL>>, 2016
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON>e <<EMAIL>>, 2014
# <AUTHOR> <EMAIL>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: Jiyo<PERSON>, Ha <<EMAIL>>\n"
"Language-Team: Korean (http://www.transifex.com/django/django/language/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Sites"
msgstr "사이트"

msgid "The domain name cannot contain any spaces or tabs."
msgstr "도메인 이름은 공백이나 탭을 포함 할 수 없습니다."

msgid "domain name"
msgstr "도메인 명"

msgid "display name"
msgstr "표시명"

msgid "site"
msgstr "사이트"

msgid "sites"
msgstr "사이트들"
