.start-page {
  min-height: 100vh;
  padding: var(--spacing-xl) 0;
}

.start-page__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-2xl);
  gap: var(--spacing-xl);
}

.start-page__welcome h1 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-sm);
}

.start-page__subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin: 0;
}

.start-page__stats {
  display: flex;
  gap: var(--spacing-md);
}

.stat-card {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  text-align: center;
  min-width: 120px;
  box-shadow: var(--shadow-sm);
}

.stat-card__value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  line-height: 1;
}

.stat-card__label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-top: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
}

.start-page__content {
  margin-top: var(--spacing-2xl);
}

.start-page__section {
  margin-bottom: var(--spacing-2xl);
}

.start-page__section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-lg);
}

.analyses-grid {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.start-page__empty-state {
  margin-top: var(--spacing-2xl);
}

.empty-state {
  text-align: center;
  padding: var(--spacing-2xl);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-xl);
  max-width: 500px;
  margin: 0 auto;
}

.empty-state__icon {
  color: var(--color-text-muted);
  margin-bottom: var(--spacing-lg);
}

.empty-state__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
}

.empty-state__description {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

/* Loading and Error States */
.start-page__loading,
.start-page__error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-border);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-lg);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.start-page__loading p,
.start-page__error p {
  color: var(--color-text-secondary);
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-lg);
}

.start-page__error h2 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
}

/* Responsive Design */
@media (max-width: 768px) {
  .start-page {
    padding: var(--spacing-lg) 0;
  }

  .start-page__header {
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  .start-page__welcome h1 {
    font-size: var(--font-size-2xl);
  }

  .start-page__subtitle {
    font-size: var(--font-size-base);
  }

  .analyses-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .stat-card {
    min-width: 100px;
    padding: var(--spacing-md);
  }

  .stat-card__value {
    font-size: var(--font-size-xl);
  }
}

@media (max-width: 480px) {
  .start-page__stats {
    width: 100%;
    justify-content: center;
  }

  .empty-state {
    padding: var(--spacing-xl);
  }
}