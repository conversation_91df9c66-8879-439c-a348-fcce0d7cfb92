import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { apiService } from '../services/api';
import './AnalysisPage.css';

const AnalysisPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [analysis, setAnalysis] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState('');

  const isNewAnalysis = id === 'new';

  useEffect(() => {
    if (isNewAnalysis) {
      setAnalysis({
        name: 'Untitled Analysis',
        description: '',
        created_at: new Date().toISOString(),
        last_edited_at: new Date().toISOString(),
        datasets: []
      });
      setEditName('Untitled Analysis');
      setLoading(false);
    } else {
      fetchAnalysis();
    }
  }, [id, isNewAnalysis]);

  const fetchAnalysis = async () => {
    try {
      setLoading(true);
      const response = await apiService.getAnalysis(id);
      setAnalysis(response.data);
      setEditName(response.data.name);
      setError(null);
    } catch (err) {
      setError('Failed to load analysis. Please try again.');
      console.error('Error fetching analysis:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveName = async () => {
    try {
      if (isNewAnalysis) {
        // Create new analysis
        const response = await apiService.createAnalysis({
          name: editName,
          description: analysis.description
        });
        setAnalysis(response.data);
        navigate(`/analysis/${response.data.id}`, { replace: true });
      } else {
        // Update existing analysis
        const response = await apiService.updateAnalysis(id, {
          name: editName
        });
        setAnalysis(response.data);
      }
      setIsEditing(false);
    } catch (err) {
      console.error('Error saving analysis name:', err);
      setError('Failed to save analysis name.');
    }
  };

  const handleCancelEdit = () => {
    setEditName(analysis.name);
    setIsEditing(false);
  };

  const handleBackToHome = () => {
    navigate('/');
  };

  if (loading) {
    return (
      <div className="analysis-page">
        <div className="container">
          <div className="analysis-page__loading">
            <div className="loading-spinner"></div>
            <p>Loading analysis...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="analysis-page">
        <div className="container">
          <div className="analysis-page__error">
            <h2>Something went wrong</h2>
            <p>{error}</p>
            <div className="analysis-page__error-actions">
              <button className="btn btn-primary" onClick={fetchAnalysis}>
                Try Again
              </button>
              <button className="btn btn-outline" onClick={handleBackToHome}>
                Back to Home
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="analysis-page">
      <div className="container">
        <header className="analysis-page__header">
          <button className="btn btn-outline analysis-page__back-btn" onClick={handleBackToHome}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M19 12H5"></path>
              <path d="M12 19l-7-7 7-7"></path>
            </svg>
            Back to Home
          </button>

          <div className="analysis-page__title-section">
            {isEditing ? (
              <div className="analysis-page__edit-form">
                <input
                  type="text"
                  value={editName}
                  onChange={(e) => setEditName(e.target.value)}
                  className="analysis-page__title-input"
                  autoFocus
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      handleSaveName();
                    } else if (e.key === 'Escape') {
                      handleCancelEdit();
                    }
                  }}
                />
                <div className="analysis-page__edit-actions">
                  <button className="btn btn-primary btn-sm" onClick={handleSaveName}>
                    Save
                  </button>
                  <button className="btn btn-outline btn-sm" onClick={handleCancelEdit}>
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <div className="analysis-page__title-display">
                <h1 className="analysis-page__title">{analysis.name}</h1>
                <button
                  className="analysis-page__edit-btn"
                  onClick={() => setIsEditing(true)}
                  title="Edit analysis name"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                  </svg>
                </button>
              </div>
            )}
          </div>
        </header>

        <main className="analysis-page__content">
          <div className="analysis-page__placeholder">
            <div className="placeholder-content">
              <div className="placeholder-content__icon">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                  <path d="M9 19c-5 0-8-3-8-8s3-8 8-8 8 3 8 8-3 8-8 8z"></path>
                  <path d="M21 21l-4.35-4.35"></path>
                </svg>
              </div>
              <h3 className="placeholder-content__title">Analysis View Coming Soon</h3>
              <p className="placeholder-content__description">
                This is where the detailed analysis interface will be implemented.
                For now, you can edit the analysis name using the pencil icon above.
              </p>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default AnalysisPage;