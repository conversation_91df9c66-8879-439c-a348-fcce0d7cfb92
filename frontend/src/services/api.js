import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// API functions
export const apiService = {
  // Dashboard
  getDashboard: () => api.get('/dashboard/'),

  // Analyses
  getAnalyses: () => api.get('/analyses/'),
  getAnalysis: (id) => api.get(`/analyses/${id}/`),
  createAnalysis: (data) => api.post('/analyses/', data),
  updateAnalysis: (id, data) => api.patch(`/analyses/${id}/`, data),
  deleteAnalysis: (id) => api.delete(`/analyses/${id}/`),

  // Datasets
  getDatasets: (analysisId) => api.get(`/analyses/${analysisId}/datasets/`),
  getDataset: (id) => api.get(`/datasets/${id}/`),
  createDataset: (analysisId, data) => api.post(`/analyses/${analysisId}/datasets/`, data),
  updateDataset: (id, data) => api.patch(`/datasets/${id}/`, data),
  deleteDataset: (id) => api.delete(`/datasets/${id}/`),
};

export default api;