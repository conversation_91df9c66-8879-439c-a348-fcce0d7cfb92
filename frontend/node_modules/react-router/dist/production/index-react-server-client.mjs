/**
 * react-router v7.8.2
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
"use client";
import {
  Await,
  BrowserRouter,
  Form,
  HashRouter,
  HistoryRouter,
  Link,
  Links,
  MemoryRouter,
  Meta,
  NavLink,
  Navigate,
  Outlet,
  Route,
  Router,
  RouterProvider,
  Routes,
  ScrollRestoration,
  StaticRouter,
  StaticRouterProvider,
  WithComponentProps,
  WithErrorBoundaryProps,
  WithHydrateFallbackProps
} from "./chunk-REDRD2MB.mjs";
export {
  Await,
  BrowserRouter,
  Form,
  HashRouter,
  Link,
  Links,
  MemoryRouter,
  Meta,
  NavLink,
  Navigate,
  Outlet,
  Route,
  Router,
  RouterProvider,
  Routes,
  ScrollRestoration,
  StaticRouter,
  StaticRouterProvider,
  WithComponentProps as UNSAFE_WithComponentProps,
  WithErrorBoundaryProps as UNSAFE_WithErrorBoundaryProps,
  WithHydrateFallbackProps as UNSAFE_WithHydrateFallbackProps,
  HistoryRouter as unstable_HistoryRouter
};
