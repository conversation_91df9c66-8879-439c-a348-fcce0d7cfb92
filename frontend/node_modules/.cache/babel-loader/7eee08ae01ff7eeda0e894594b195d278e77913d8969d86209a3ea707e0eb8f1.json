{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Coding/tableyze/frontend/src/components/AnalysisCard.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport './AnalysisCard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AnalysisCard = ({\n  analysis,\n  isNewCard = false\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const handleClick = () => {\n    if (isNewCard) {\n      navigate('/analysis/new');\n    } else {\n      navigate(`/analysis/${analysis.id}`);\n    }\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n  if (isNewCard) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"analysis-card analysis-card--new card card-clickable\",\n      onClick: handleClick,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"analysis-card__content analysis-card__content--new\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analysis-card__icon\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"48\",\n            height: \"48\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            children: [/*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"12\",\n              y1: \"5\",\n              x2: \"12\",\n              y2: \"19\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"5\",\n              y1: \"12\",\n              x2: \"19\",\n              y2: \"12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"analysis-card__title\",\n          children: \"New Analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"analysis-card__subtitle\",\n          children: \"Start a new data analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"analysis-card card card-clickable\",\n    onClick: handleClick,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"analysis-card__content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"analysis-card__header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"analysis-card__title\",\n          children: analysis.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analysis-card__meta\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"analysis-card__date\",\n            children: formatDate(analysis.last_edited_at)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), analysis.datasets_count > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"analysis-card__datasets\",\n            children: [analysis.datasets_count, \" dataset\", analysis.datasets_count !== 1 ? 's' : '']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), analysis.description && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"analysis-card__description\",\n        children: analysis.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"analysis-card__footer\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"analysis-card__created\",\n          children: [\"Created \", formatDate(analysis.created_at)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 5\n  }, this);\n};\n_s(AnalysisCard, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = AnalysisCard;\nexport default AnalysisCard;\nvar _c;\n$RefreshReg$(_c, \"AnalysisCard\");", "map": {"version": 3, "names": ["React", "useNavigate", "jsxDEV", "_jsxDEV", "AnalysisCard", "analysis", "isNewCard", "_s", "navigate", "handleClick", "id", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "className", "onClick", "children", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "x1", "y1", "x2", "y2", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "last_edited_at", "datasets_count", "description", "created_at", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Coding/tableyze/frontend/src/components/AnalysisCard.js"], "sourcesContent": ["import React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport './AnalysisCard.css';\n\nconst AnalysisCard = ({ analysis, isNewCard = false }) => {\n  const navigate = useNavigate();\n\n  const handleClick = () => {\n    if (isNewCard) {\n      navigate('/analysis/new');\n    } else {\n      navigate(`/analysis/${analysis.id}`);\n    }\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  if (isNewCard) {\n    return (\n      <div className=\"analysis-card analysis-card--new card card-clickable\" onClick={handleClick}>\n        <div className=\"analysis-card__content analysis-card__content--new\">\n          <div className=\"analysis-card__icon\">\n            <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n              <line x1=\"12\" y1=\"5\" x2=\"12\" y2=\"19\"></line>\n              <line x1=\"5\" y1=\"12\" x2=\"19\" y2=\"12\"></line>\n            </svg>\n          </div>\n          <h3 className=\"analysis-card__title\">New Analysis</h3>\n          <p className=\"analysis-card__subtitle\">Start a new data analysis</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"analysis-card card card-clickable\" onClick={handleClick}>\n      <div className=\"analysis-card__content\">\n        <div className=\"analysis-card__header\">\n          <h3 className=\"analysis-card__title\">{analysis.name}</h3>\n          <div className=\"analysis-card__meta\">\n            <span className=\"analysis-card__date\">\n              {formatDate(analysis.last_edited_at)}\n            </span>\n            {analysis.datasets_count > 0 && (\n              <span className=\"analysis-card__datasets\">\n                {analysis.datasets_count} dataset{analysis.datasets_count !== 1 ? 's' : ''}\n              </span>\n            )}\n          </div>\n        </div>\n\n        {analysis.description && (\n          <p className=\"analysis-card__description\">{analysis.description}</p>\n        )}\n\n        <div className=\"analysis-card__footer\">\n          <span className=\"analysis-card__created\">\n            Created {formatDate(analysis.created_at)}\n          </span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AnalysisCard;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,SAAS,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EACxD,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAE9B,MAAMQ,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIH,SAAS,EAAE;MACbE,QAAQ,CAAC,eAAe,CAAC;IAC3B,CAAC,MAAM;MACLA,QAAQ,CAAC,aAAaH,QAAQ,CAACK,EAAE,EAAE,CAAC;IACtC;EACF,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,IAAIZ,SAAS,EAAE;IACb,oBACEH,OAAA;MAAKgB,SAAS,EAAC,sDAAsD;MAACC,OAAO,EAAEX,WAAY;MAAAY,QAAA,eACzFlB,OAAA;QAAKgB,SAAS,EAAC,oDAAoD;QAAAE,QAAA,gBACjElB,OAAA;UAAKgB,SAAS,EAAC,qBAAqB;UAAAE,QAAA,eAClClB,OAAA;YAAKmB,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAAAN,QAAA,gBAC/FlB,OAAA;cAAMyB,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5ChC,OAAA;cAAMyB,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNhC,OAAA;UAAIgB,SAAS,EAAC,sBAAsB;UAAAE,QAAA,EAAC;QAAY;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtDhC,OAAA;UAAGgB,SAAS,EAAC,yBAAyB;UAAAE,QAAA,EAAC;QAAyB;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEhC,OAAA;IAAKgB,SAAS,EAAC,mCAAmC;IAACC,OAAO,EAAEX,WAAY;IAAAY,QAAA,eACtElB,OAAA;MAAKgB,SAAS,EAAC,wBAAwB;MAAAE,QAAA,gBACrClB,OAAA;QAAKgB,SAAS,EAAC,uBAAuB;QAAAE,QAAA,gBACpClB,OAAA;UAAIgB,SAAS,EAAC,sBAAsB;UAAAE,QAAA,EAAEhB,QAAQ,CAAC+B;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzDhC,OAAA;UAAKgB,SAAS,EAAC,qBAAqB;UAAAE,QAAA,gBAClClB,OAAA;YAAMgB,SAAS,EAAC,qBAAqB;YAAAE,QAAA,EAClCV,UAAU,CAACN,QAAQ,CAACgC,cAAc;UAAC;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,EACN9B,QAAQ,CAACiC,cAAc,GAAG,CAAC,iBAC1BnC,OAAA;YAAMgB,SAAS,EAAC,yBAAyB;YAAAE,QAAA,GACtChB,QAAQ,CAACiC,cAAc,EAAC,UAAQ,EAACjC,QAAQ,CAACiC,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL9B,QAAQ,CAACkC,WAAW,iBACnBpC,OAAA;QAAGgB,SAAS,EAAC,4BAA4B;QAAAE,QAAA,EAAEhB,QAAQ,CAACkC;MAAW;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CACpE,eAEDhC,OAAA;QAAKgB,SAAS,EAAC,uBAAuB;QAAAE,QAAA,eACpClB,OAAA;UAAMgB,SAAS,EAAC,wBAAwB;UAAAE,QAAA,GAAC,UAC/B,EAACV,UAAU,CAACN,QAAQ,CAACmC,UAAU,CAAC;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5B,EAAA,CAlEIH,YAAY;EAAA,QACCH,WAAW;AAAA;AAAAwC,EAAA,GADxBrC,YAAY;AAoElB,eAAeA,YAAY;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}