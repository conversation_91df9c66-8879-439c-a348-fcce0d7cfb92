{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('authToken');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor for error handling\napi.interceptors.response.use(response => response, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    localStorage.removeItem('authToken');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\n\n// API functions\nexport const apiService = {\n  // Dashboard\n  getDashboard: () => api.get('/dashboard/'),\n  // Analyses\n  getAnalyses: () => api.get('/analyses/'),\n  getAnalysis: id => api.get(`/analyses/${id}/`),\n  createAnalysis: data => api.post('/analyses/', data),\n  updateAnalysis: (id, data) => api.patch(`/analyses/${id}/`, data),\n  deleteAnalysis: id => api.delete(`/analyses/${id}/`),\n  // Datasets\n  getDatasets: analysisId => api.get(`/analyses/${analysisId}/datasets/`),\n  getDataset: id => api.get(`/datasets/${id}/`),\n  createDataset: (analysisId, data) => api.post(`/analyses/${analysisId}/datasets/`, data),\n  updateDataset: (id, data) => api.patch(`/datasets/${id}/`, data),\n  deleteDataset: id => api.delete(`/datasets/${id}/`)\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "apiService", "getDashboard", "get", "getAnalyses", "getAnalysis", "id", "createAnalysis", "data", "post", "updateAnalysis", "patch", "deleteAnalysis", "delete", "getDatasets", "analysisId", "getDataset", "createDataset", "updateDataset", "deleteDataset"], "sources": ["/Users/<USER>/Desktop/Coding/tableyze/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('authToken');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor for error handling\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      localStorage.removeItem('authToken');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// API functions\nexport const apiService = {\n  // Dashboard\n  getDashboard: () => api.get('/dashboard/'),\n\n  // Analyses\n  getAnalyses: () => api.get('/analyses/'),\n  getAnalysis: (id) => api.get(`/analyses/${id}/`),\n  createAnalysis: (data) => api.post('/analyses/', data),\n  updateAnalysis: (id, data) => api.patch(`/analyses/${id}/`, data),\n  deleteAnalysis: (id) => api.delete(`/analyses/${id}/`),\n\n  // Datasets\n  getDatasets: (analysisId) => api.get(`/analyses/${analysisId}/datasets/`),\n  getDataset: (id) => api.get(`/datasets/${id}/`),\n  createDataset: (analysisId, data) => api.post(`/analyses/${analysisId}/datasets/`, data),\n  updateDataset: (id, data) => api.patch(`/datasets/${id}/`, data),\n  deleteDataset: (id) => api.delete(`/datasets/${id}/`),\n};\n\nexport default api;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAEjF,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EAC/C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,GAAG,CAACI,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClCR,YAAY,CAACS,UAAU,CAAC,WAAW,CAAC;IACpCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMU,UAAU,GAAG;EACxB;EACAC,YAAY,EAAEA,CAAA,KAAMvB,GAAG,CAACwB,GAAG,CAAC,aAAa,CAAC;EAE1C;EACAC,WAAW,EAAEA,CAAA,KAAMzB,GAAG,CAACwB,GAAG,CAAC,YAAY,CAAC;EACxCE,WAAW,EAAGC,EAAE,IAAK3B,GAAG,CAACwB,GAAG,CAAC,aAAaG,EAAE,GAAG,CAAC;EAChDC,cAAc,EAAGC,IAAI,IAAK7B,GAAG,CAAC8B,IAAI,CAAC,YAAY,EAAED,IAAI,CAAC;EACtDE,cAAc,EAAEA,CAACJ,EAAE,EAAEE,IAAI,KAAK7B,GAAG,CAACgC,KAAK,CAAC,aAAaL,EAAE,GAAG,EAAEE,IAAI,CAAC;EACjEI,cAAc,EAAGN,EAAE,IAAK3B,GAAG,CAACkC,MAAM,CAAC,aAAaP,EAAE,GAAG,CAAC;EAEtD;EACAQ,WAAW,EAAGC,UAAU,IAAKpC,GAAG,CAACwB,GAAG,CAAC,aAAaY,UAAU,YAAY,CAAC;EACzEC,UAAU,EAAGV,EAAE,IAAK3B,GAAG,CAACwB,GAAG,CAAC,aAAaG,EAAE,GAAG,CAAC;EAC/CW,aAAa,EAAEA,CAACF,UAAU,EAAEP,IAAI,KAAK7B,GAAG,CAAC8B,IAAI,CAAC,aAAaM,UAAU,YAAY,EAAEP,IAAI,CAAC;EACxFU,aAAa,EAAEA,CAACZ,EAAE,EAAEE,IAAI,KAAK7B,GAAG,CAACgC,KAAK,CAAC,aAAaL,EAAE,GAAG,EAAEE,IAAI,CAAC;EAChEW,aAAa,EAAGb,EAAE,IAAK3B,GAAG,CAACkC,MAAM,CAAC,aAAaP,EAAE,GAAG;AACtD,CAAC;AAED,eAAe3B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}