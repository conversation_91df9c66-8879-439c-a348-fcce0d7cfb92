[{"/Users/<USER>/Desktop/Coding/tableyze/frontend/src/index.js": "1", "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/reportWebVitals.js": "2", "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/App.js": "3", "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/pages/AnalysisPage.js": "4", "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/pages/StartPage.js": "5", "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/components/AnalysisCard.js": "6", "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/services/api.js": "7"}, {"size": 486, "mtime": 1756752975271, "results": "8", "hashOfConfig": "9"}, {"size": 362, "mtime": 1756751016190, "results": "10", "hashOfConfig": "9"}, {"size": 505, "mtime": 1756752958427, "results": "11", "hashOfConfig": "9"}, {"size": 6430, "mtime": 1756752891349, "results": "12", "hashOfConfig": "9"}, {"size": 3906, "mtime": 1756752759263, "results": "13", "hashOfConfig": "9"}, {"size": 2324, "mtime": 1756752646713, "results": "14", "hashOfConfig": "9"}, {"size": 1600, "mtime": 1756752605285, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1tntbk5", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/index.js", [], [], "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/reportWebVitals.js", [], [], "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/App.js", [], [], "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/pages/AnalysisPage.js", ["37"], [], "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/pages/StartPage.js", [], [], "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/components/AnalysisCard.js", [], [], "/Users/<USER>/Desktop/Coding/tableyze/frontend/src/services/api.js", [], [], {"ruleId": "38", "severity": 1, "message": "39", "line": 31, "column": 6, "nodeType": "40", "endLine": 31, "endColumn": 25, "suggestions": "41"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchAnalysis'. Either include it or remove the dependency array.", "ArrayExpression", ["42"], {"desc": "43", "fix": "44"}, "Update the dependencies array to be: [fetchAnalysis, id, isNewAnalysis]", {"range": "45", "text": "46"}, [927, 946], "[fetchAnalysis, id, isNewAnalysis]"]